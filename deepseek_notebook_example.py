# DeepSeek API 调用示例 - Jupyter Notebook版本
# 请在Jupyter Notebook中逐个运行以下代码块

# ============ 第一个代码块：导入库 ============
from openai import OpenAI
import os

# ============ 第二个代码块：设置API密钥 ============
# 方法1：直接设置环境变量（推荐）
# 在终端中运行：export DEEPSEEK_API_KEY="your_api_key_here"
# 在终端中运行：export DEEPSEEK_PROXY="your_proxy_url_here"

# 方法2：在代码中直接设置（不推荐，有安全风险）
# os.environ["DEEPSEEK_API_KEY"] = "your_api_key_here"
# os.environ["DEEPSEEK_PROXY"] = "your_proxy_url_here"

# 获取API密钥和代理
apiKey = os.getenv("DEEPSEEK_API_KEY")
proxy = os.getenv("DEEPSEEK_PROXY")

print(f"API Key 已设置: {'是' if apiKey else '否'}")
print(f"代理已设置: {'是' if proxy else '否'}")

# ============ 第三个代码块：创建客户端 ============
if not apiKey:
    print("❌ 请先设置 DEEPSEEK_API_KEY 环境变量")
else:
    client = OpenAI(
        api_key=apiKey, 
        base_url=proxy or "https://api.deepseek.com"
    )
    print("✅ OpenAI客户端创建成功")

# ============ 第四个代码块：调用API ============
try:
    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"}
        ],
        stream=False
    )
    
    print("✅ API调用成功!")
    print(f"回复: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ API调用失败: {e}")
    print("请检查:")
    print("1. API密钥是否正确")
    print("2. 网络连接是否正常")
    print("3. 代理设置是否正确")

# ============ 第五个代码块：验证安装 ============
print("\n=== 环境验证 ===")
print(f"Python版本: {os.sys.version}")
print(f"OpenAI库版本: {OpenAI.__module__}")
print("✅ 所有依赖已正确安装") 