#!/usr/bin/env python3
"""
Python 3.10.12 环境测试脚本
"""

import sys
import platform

def main():
    print("=" * 50)
    print("Python 环境信息")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台信息: {platform.platform()}")
    print(f"架构: {platform.architecture()}")
    
    print("\n" + "=" * 50)
    print("测试基本功能")
    print("=" * 50)
    
    # 测试基本功能
    print("Hello from Python 3.10.12!")
    
    # 测试一些Python 3.10的特性
    try:
        # 结构化模式匹配 (Python 3.10新特性)
        def test_match(value):
            match value:
                case 1:
                    return "一"
                case 2:
                    return "二"
                case _:
                    return "其他"
        
        print(f"模式匹配测试: {test_match(1)}")
        print("✅ Python 3.10特性正常工作")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main() 