# Arthas使用指南

## 什么是Arthas？

Arthas（阿尔萨斯）是阿里巴巴开源的Java诊断工具，深受开发者喜爱的Java线上问题诊断工具，提供了丰富的功能帮助开发人员对Java应用进行问题排查、实时监控和性能分析。

## Arthas主要功能

Arthas支持JDK 6+，支持Linux/Mac/Windows，采用命令行交互模式，简单易用，功能强大。主要功能如下：

### 1. 基础功能
- **查看JVM信息**：查看JVM运行状态、参数配置等
- **线程分析**：查看线程堆栈、死锁检测、繁忙线程排查
- **类加载分析**：查看类加载器结构、统计类加载信息
- **内存分析**：查看JVM内存使用情况，查找内存泄漏

### 2. 高级功能
- **方法执行**：支持方法执行耗时统计、调用次数统计
- **异常排查**：监控方法抛出的异常信息
- **热更新**：动态更新已加载类的代码
- **性能分析**：CPU使用率分析、内存分配速率等
- **火焰图**：生成应用热点火焰图

## 安装方式

### 在线安装（推荐）

```bash
curl -O https://arthas.aliyun.com/arthas-boot.jar
java -jar arthas-boot.jar
```

### 离线安装

```bash
# 下载离线包
curl -O https://arthas.aliyun.com/download/latest_version/arthas-packaging-bin.zip
# 解压
unzip arthas-packaging-bin.zip
# 运行
cd arthas
./as.sh
```

## 常用命令详解

### 1. 基础命令

| 命令 | 功能说明 |
|------|----------|
| help | 查看命令帮助信息 |
| dashboard | 显示系统实时数据面板，包括线程、内存、GC等 |
| thread | 查看当前线程信息，分析线程状态 |
| jvm | 查看JVM信息 |
| sysprop | 查看和修改JVM系统属性 |
| sysenv | 查看JVM环境变量 |
| vmoption | 查看和修改JVM启动参数 |
| logger | 查看和修改logger级别 |
| getstatic | 获取类的静态字段值 |
| ognl | 执行ognl表达式 |

### 2. 类/方法相关

| 命令 | 功能说明 |
|------|----------|
| sc | 查看JVM已加载的类信息 |
| sm | 查看已加载类的方法信息 |
| jad | 反编译指定已加载类的源码 |
| mc | 内存编译器，内存中编译.java文件为.class文件 |
| redefine | 加载外部的.class文件，重定义JVM已加载的类 |

### 3. 监控类

| 命令 | 功能说明 |
|------|----------|
| monitor | 方法执行监控，统计方法调用次数、成功率、耗时等 |
| watch | 方法执行数据观测，监控方法调用前后的参数、返回值和异常 |
| trace | 方法内部调用路径，并输出方法路径上的每个节点上耗时 |
| stack | 输出当前方法被调用的调用路径 |
| tt | 方法执行数据的时空隧道，记录方法每次调用的入参和返回信息 |
| profiler | 使用async-profiler生成火焰图 |

## 使用场景示例

### 1. 排查CPU高负载问题

```bash
# 查看系统整体情况
dashboard

# 查看最繁忙的线程 
thread -n 3

# 查看线程的堆栈信息
thread tid

# 生成CPU火焰图，分析热点方法
profiler start
# 等待30秒
profiler stop
```

### 2. 排查方法执行慢的问题

```bash
# 监控方法的执行耗时
monitor -c 5 com.example.Service method1

# 查看方法执行路径和耗时
trace com.example.Service method1

# 监控方法进入和返回的参数
watch com.example.Service method1 "{params,returnObj}" -x 3
```

### 3. OOM内存分析

```bash
# 查看内存使用情况
dashboard

# 查看占用内存最大的对象
heapdump --live /tmp/dump.hprof
```

### 4. 热修复线上代码

```bash
# 反编译要修改的类
jad --source-only com.example.Service > Service.java

# 修改源码 Service.java

# 编译修改后的类
mc Service.java -d /tmp

# 重新加载定义
redefine /tmp/com/example/Service.class
```

## 注意事项

1. **权限问题**：Arthas需要具有JVM进程的操作权限
2. **性能影响**：部分命令（如trace）会对性能有一定影响，建议在低流量时段使用
3. **类冲突**：Arthas自身也是一个Java程序，可能与应用类存在冲突
4. **热更新限制**：redefine不能增加/删除方法，不能修改方法签名和静态属性

## 进阶使用

### 1. Arthas的Web Console

启动Arthas时指定参数开启Web Console：

```bash
java -jar arthas-boot.jar --target-ip 0.0.0.0
```

### 2. 使用Arthas的TunnelServer远程诊断

```bash
java -jar arthas-tunnel-server.jar --server.port=7777
```

### 3. Arthas脚本执行

可以将常用命令组装成批处理脚本：

```bash
java -jar arthas-boot.jar --command "thread -n 3;dashboard;jvm"
```

## 资源链接

- [Arthas GitHub项目](https://github.com/alibaba/arthas)
- [Arthas官方文档](https://arthas.aliyun.com/doc/)
- [Arthas进阶教程](https://arthas.aliyun.com/doc/arthas-tutorials.html) 