{"basicFilterCondition": [{"fieldName": "status", "inbound": true, "operator": "NIN", "order": 1, "outbound": true, "values": [-1]}], "deleteOriginalData": false, "fieldDescribe": [{"fieldBizType": "Name", "fieldType": "String", "name": "name", "order": 2147483647}, {"fieldBizType": "ApiName", "fieldType": "String", "name": "apiName", "order": 2}, {"fieldType": "Number", "name": "status", "order": 2}, {"fieldType": "String", "name": "description", "order": 2}, {"fieldType": "List", "name": "channelList", "order": 2}, {"fieldBizType": "TenantId", "fieldType": "Number", "name": "tenantId", "order": 2}, {"fieldBizType": "ObjectId", "fieldType": "String", "name": "id", "order": 2}, {"fieldBizType": "UserId", "fieldType": "Number", "name": "creatorId", "order": 2}, {"fieldBizType": "DateFormat", "fieldType": "TimeStamp", "name": "createTime", "order": 2}, {"fieldBizType": "UserId", "fieldType": "Number", "name": "updateId", "order": 2}, {"fieldBizType": "DateFormat", "fieldType": "TimeStamp", "name": "updateTime", "order": 2}], "inBoundCheck": {"carefulChecks": [{"filterGroup": [{"filters": [{"fieldName": "api_name", "operator": "IN", "order": 2147483647, "values": ["version_number", "changed_by", "changed_time", "changed_reason", "changed_status"]}]}], "key": "obj_change_order_SaleContractObj", "type": 2}], "checkTableDescriptors": [{"defaultFormat": "对象{0}对应的字段{1}已存在(忽略大小写)，请删除该字段后重新入站", "filters": [{"fieldName": "status", "operator": "NIN", "order": 1, "values": ["deleted"]}], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "api_name", "operator": "IEQ", "refFieldName": "api_name"}, {"fieldName": "api_name", "operator": "NIN", "refFieldName": "api_name"}], "i18nFormatKey": "", "operator": "NIN", "paramFields": ["describe_api_name", "api_name"], "processLack": "exception", "tableId": "paas_metadata_mt_field"}], "dependencyTableDescriptors": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}, {"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}], "paramFields": [], "processLack": "exception", "tableId": "paas_metadata_mt_describe"}], "unchangeableField": [{"fieldName": "type", "paramFields": []}], "uniqueIndices": []}, "logicDeleteFieldValue": {}, "order": 0, "principalIds": [], "processorDescribe": {"inBoundTag": "PG", "outBoundTag": "PG", "selectHandlerType": {"configName": "PG", "handlerType": "PG"}}, "slaveTableDescribe": [{"filters": [], "foreignKeyDescribe": [{"fieldName": "tenant_id", "operator": "IN", "refFieldName": "tenant_id"}, {"fieldName": "describe_api_name", "operator": "IN", "refFieldName": "describe_api_name"}], "tableId": "multi_organization_action_mt_field"}], "tableId": "paas_ui_paas_work_space", "tableName": "WorkSpaceEntity", "uniqueKeys": ["tenant_id", "describe_api_name", "api_name"]}