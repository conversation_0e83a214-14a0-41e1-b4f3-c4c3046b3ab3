@startuml MDC决策流程图

skinparam {
  BackgroundColor white
  ArrowColor #368dff
  ActivityBackgroundColor #f0f9ff
  ActivityBorderColor #0c6cff
  ActivityDiamondBackgroundColor #e6f4ff
  ActivityDiamondBorderColor #004fd9
  NoteBackgroundColor #b0daff
}

title fs-paas-appframework 单元测试MDC选择流程

start

:所有单元测试必须基于 **@301-appframework-base-unit-demo.mdc**;

if (类名后缀?) then (Action/Controller)
  if (调用父类方法?) then (是)
    #87c3ff:使用 **@301-appframework-bussiness.mdc**;
    note right
      必须使用PowerMock处理super调用
    end note
  else (否)
    #5ea9ff:使用 **@300-generate-unit-test.mdc**;
  endif
else (其他)
  #5ea9ff:使用 **@300-generate-unit-test.mdc**;
  note right
    适用于非Action/Controller类
  end note
endif

stop

@enduml