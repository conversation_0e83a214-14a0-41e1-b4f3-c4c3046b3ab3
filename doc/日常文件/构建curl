curl 'https://www.fxiaoke.com/FHH/EM1HWebPage/dropList/getMenuDropList?_fs_token=DsPaCZ9YDMOjCp8sDoqqC6DYBJbcE3GjDpWpP3DXEJGnDsGq&traceId=E-E.isearch.1219-19481996' \
  -H 'accept: application/json, text/javascript, */*; q=0.01' \
  -H 'accept-language: zh-CN,zh-TW;0.9,en;0.8' \
  -H 'content-type: application/json; charset=UTF-8' \
  -H 'cookie: guid=6adcdf58-1bd4-a525-d7b4-c1956b02df25; fsRoutes="qb01tvEyco2Jv4sEzBYxrvxdEIvxXGNPnbbh4Eg+lr4="; EROuterTenantId=300007850; EROuterUid=311032760; ERInfo=er_9fcc719f46f1456e81bf83329deb4315_0512174135_590056; CRInfo=er_9fcc719f46f1456e81bf83329deb4315_0512174135_590056; ERUpstreamEa=fktest079; fs_token=DsPaCZ9YDMOjCp8sDoqqC6DYBJbcE3GjDpWpP3DXEJGnDsGq; FSAuthX=0G60uCm51WC0003WULSMaQ94kz1urUV7Bt2yz2HKQn85IQAwOCzszKtzUT7PKjfs4zu2TSyQ2scY6B4YHRyy04OMtucotf2PD8220m8zkGep48DG7qLzfzq6MHDdTtIz7Hf8NVZBMMtzv42lyRa5f8ieetxycZmck2FE6SEGksJC8vYCadyrG3i2yyq0Ix4I1VdtMJBTHHDLiMQJYp; FSAuthXC=0G60uCm51WC0003WULSMaQ94kz1urUV7Bt2yz2HKQn85IQAwOCzszKtzUT7PKjfs4zu2TSyQ2scY6B4YHRyy04OMtucotf2PD8220m8zkGep48DG7qLzfzq6MHDdTtIz7Hf8NVZBMMtzv42lyRa5f8ieetxycZmck2FE6SEGksJC8vYCadyrG3i2yyq0Ix4I1VdtMJBTHHDLiMQJYp; lang=zh-CN; JSESSIONID=1319667C4AFA665B5706040BEB8D6F14' \
  -H 'origin: https://www.fxiaoke.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://www.fxiaoke.com/XV/UI/manage' \
  -H 'sec-ch-ua: "Not/A)Brand";v="8", "Chromium";v="126"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Biscuit/1.2.30 Chrome/********* Safari/537.36' \
  -H 'x-requested-with: XMLHttpRequest' \
  -H 'x-trace-id: isearch_1219_1747192451233:51' \
  --data-raw '{"appId":"CRM"}'



  curl --request POST \
  --url http://localhost/dropList/getMenuDropList \
  --header 'X-fs-Employee-Id: 1219' \
  --header 'X-fs-Enterprise-Account: isearch' \
  --header 'X-fs-Enterprise-Id: 716476' \
  --header 'content-type: application/json' \
  --data '{"appId":"CRM"}'

  watch com.facishare.webpage.customer.remote.ObjectService getAllDescribe '{params,returnObj,throwExp}'  -n 5  -x 3 



  ognl '@<EMAIL>("716476-zh-CN")'


ognl '#instance=@com.facishare.webpage.customer.remote.impl.ObjectServiceImpl@INSTANCES[0],#instance.webpageRedis.del("716476-zh-CN")' -x 1
