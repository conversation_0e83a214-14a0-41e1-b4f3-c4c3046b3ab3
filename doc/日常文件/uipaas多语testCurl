/paasAppManager/getAppList
/webPage/homePageRestService/getHomePageLayoutById
/webPage/homePageRestService/getHomePageLabelNameById
/webPage/PaaSAppRestService/getAppList
/webPage/PaaSAppRestService/getAppListForTransLate
/webPage/webPage/getAppListByTypeAndHasEnableAppView


有
curl --request POST \
  --url http://localhost/paasAppManager/getAppList \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --data '{"_isfilter":false}'


无
  curl --request POST \
  --url http://localhost/webPage/homePageRestService/getHomePageLayoutById \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --data '{
    "LayoutID": "590064_a0e8466a5ac04643a9aa4656048aa749",
    "enterpriseId": 590064,
    "enterpriseAccount": "590064",
    "employeeId": 1000,
    "translateFlag": true
  }'


无
  curl --request POST \
  --url http://localhost/webPage/homePageRestService/getHomePageLabelNameById \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --data '{
    "layoutId": "590064_a0e8466a5ac04643a9aa4656048aa749",
    "enterpriseId": 590064,
    "enterpriseAccount": "590064",
    "employeeId": 1000,
    "translateFlag": true
  }' -i



有  
  curl --request POST \
  --url http://localhost/webPage/PaaSAppRestService/getAppList \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --data '{
    "enterpriseId": 590064,
    "enterpriseAccount": "590064",
    "employeeId": 1000,
    "hasPermission": true,
    "translateFlag": true
  }' -i


无
  curl --request POST \
  --url http://localhost/webPage/PaaSAppRestService/getAppListForTransLate \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --data '{
    "enterpriseId": 590064,
    "enterpriseAccount": "590064",
    "employeeId": 1000,
    "hasPermission": true,
    "translateFlag": true
  }' -i



有
  curl --request POST \
  --url http://localhost/webPage/getAppListByTypeAndHasEnableAppView \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 590064' \
  --header 'X-fs-Enterprise-Id: 590064' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 590064' \
  --data '{
    "type": 1,
    "userId": 1000,
    "lang": "en",
    "tenantId":590064
  }' -i