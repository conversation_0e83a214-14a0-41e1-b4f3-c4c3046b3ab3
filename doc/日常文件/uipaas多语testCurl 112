/paasAppManager/getAppList
/webPage/homePageRestService/getHomePageLayoutById
/webPage/homePageRestService/getHomePageLabelNameById
/webPage/PaaSAppRestService/getAppList
/webPage/PaaSAppRestService/getAppListForTransLate
/webPage/webPage/getAppListByTypeAndHasEnableAppView


预期 有
发布前验证 有
发布后验证 有
curl --request POST \
  --url http://localhost/paasAppManager/getAppList \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --data '{"_isfilter":false}' -i


预期 无
发布前验证 无
发布后验证 无
  curl --request POST \
  --url http://localhost/webPage/homePageRestService/getHomePageLayoutById \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --data '{
    "LayoutID": "74255_c68fdf4dcb0646278b3cc6aa535efc81",
    "enterpriseId": 74255,
    "enterpriseAccount": "74255",
    "employeeId": 1000,
    "translateFlag": true
  }' -i



预期 无
发布前验证 无
发布后验证 无
  curl --request POST \
  --url http://localhost/webPage/homePageRestService/getHomePageLabelNameById \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --data '{
    "layoutId": "74255_c68fdf4dcb0646278b3cc6aa535efc81",
    "enterpriseId": 74255,
    "enterpriseAccount": "74255",
    "employeeId": 1000,
    "translateFlag": true
  }' -i




预期 无
发布前验证 有
发布后验证 无 
  curl --request POST \
  --url http://localhost/webPage/PaaSAppRestService/getAppList \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --data '{
    "enterpriseId": 74255,
    "enterpriseAccount": "74255",
    "employeeId": 1000,
    "hasPermission": true,
    "translateFlag": true
  }' -i



预期 无
发布前验证 无
发布后验证 无
  curl --request POST \
  --url http://localhost/webPage/PaaSAppRestService/getAppListForTransLate \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --data '{
    "enterpriseId": 74255,
    "enterpriseAccount": "74255",
    "employeeId": 1000,
    "hasPermission": true,
    "translateFlag": true
  }' -i




预期 无
发布前验证 有
发布后验证 无
  curl --request POST \
  --url http://localhost/webPage/getAppListByTypeAndHasEnableAppView \
  --header 'Accept-Language: en' \
  --header 'X-fs-Employee-Id: 1000' \
  --header 'X-fs-Enterprise-Account: 74255' \
  --header 'X-fs-Enterprise-Id: 74255' \
  --header 'content-type: application/json' \
  --header 'x-fs-ei: 74255' \
  --data '{
    "type": 1,
    "userId": 1000,
    "lang": "en",
    "tenantId":74255
  }' -i