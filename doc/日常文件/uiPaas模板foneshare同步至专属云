webPage CRM数据同步到专属云
1、来源数据库地址：172.17.59.60:27017,172.17.60.60:27017,172.17.58.60:27017
2、来源数据库名:fs-webpage-customer
3、来源数据collection:参考查询语句
4、目标原库地址：专属云server_60各个专属云库
5、目标数据库名：fs-webpage-customer
6、目标数据collection：同来源collection
7、要导入的数据查询语句如下：

//应用模板信息
订货管理视图相关信息：
    订货管理:
    db.PageTemple.find({_id:"801862_2603d881c3ed4dfa830980a9f16fa33b"})
    db.TenantMenu.find({_id:"801862_66c88341dcb7439fbb700454dd434fea"})
    db.HomePageLayout.find({_id:"801862_dc9eb605fb994e8ead4b978ff41bf4b1"})
    订货管理（移动端）
    db.PageTemple.find({_id:"801862_643bc1a8ed0d42fc93b0150f700b8591"})

渠道分销管理相关信息：
    渠道默认门户(web):
    db.PageTemple.find({_id:"801862_a16704ebc2f647b88ac64752bb482656"})
    db.TenantMenu.find({_id:"801862_e4002a3176ad4ae7b18a68a1163072af"})
    db.HomePageLayout.find({_id:"801862_05e4f8f7969b440499d3f4c35fb56995"})

    渠道分销默认门户（移动端）
    db.PageTemple.find({_id:"801862_2cac68158d824a87a9ad9ad79f60200f"})

订货通相关信息：
    渠道订货（预设）（web）
    db.PageTemple.find({_id:"801862_1df97401ff6a45158b6dd35abff7812a"})
    db.TenantMenu.find({_id:"801862_a1b940a36f6e4a0cafc10461e2e050fd"})
    db.HomePageLayout.find({_id:"801862_22fb9ce752dd4e91aa641fa973f3ea2e"})
   	
    渠道订货（预设）（移动端）
    db.PageTemple.find({_id:"801862_d3514d80bc8746b9afe982d0928aa56c"})

经销商管理：
    预设页面模板(web)
    db.PageTemple.find({_id:"803778_54c7231c21d54c5abbb9cf1fb97f651a"})
    db.TenantMenu.find({_id:"803778_f8a1b5d16ca8457985bfebf13c84f6df"})
    db.HomePageLayout.find({_id:"803778_f875aa5e095749ce9af840f6a450895d"})

    移动端预设模版（移动端）
    db.PageTemple.find({_id:"803778_cb3c6a2c59f94269a8b098ec0b5c8e26"})

经销商门户（移动端）页面
     预设经销商门户	(web)
    db.PageTemple.find({_id:"803778_aac7e7aee5c64d3ba12e4232e395846e"})
    db.TenantMenu.find({_id:"803778_fd3e4dea98c84238abefc72f9a19d0aa"})
    db.HomePageLayout.find({_id:"803778_f989eb44e75548f4901ea68d27c7b075"})

    预设经销商门户	（移动端）
    db.PageTemple.find({_id:"803778_89721435058c4b9286219ad681f16686"})


userext CRM数据同步到专属云
1、来源数据库地址：172.17.59.11:27017,172.17.60.11:27017,172.17.58.11:27017
2、来源数据库名:FSUserExt
3、来源数据collection:参考查询语句
4、目标原库地址：专属云server_11各个专属云库
5、目标数据库名：FSUserExt
6、目标数据collection：同来源collection
7、要导入的数据查询语句如下：

//app模板页面信息：
订货管理（移动端）页面
db.PageTemplateV2.find({PTId:"801862-6352f4dca78d46c3bfc13deba8911780"})

渠道分销默认门户（移动端）页面
db.PageTemplateV2.find({PTId:"801862-eacb548722984323b2eedccb65e1cb50"})

渠道订货（预设）（移动端） 页面	
db.PageTemplateV2.find({PTId:"801862-41fa5a0d07244a01bd0d58510fb07a0d"})

经销商管理(移动端) 页面	
db.PageTemplateV2.find({PTId:"803778-8771ee06740e47c4bc25bd4caec6abd2"})

预设经销商门户	（移动端）页面
db.PageTemplateV2.find({PTId:"803778-c2940e29b4c5422f9e23f313147cf7f8"})