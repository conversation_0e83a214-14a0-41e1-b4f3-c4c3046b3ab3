# WhatsApp MCP服务使用示例

本文档提供了如何通过Model Context Protocol (MCP)服务操作WhatsApp网页版的示例指令。这些示例可以帮助您了解如何使用自然语言与WhatsApp进行交互。

## 登录WhatsApp Web

第一次使用时，您需要登录WhatsApp Web。MCP服务会自动打开浏览器，您需要扫描二维码登录。

```
请帮我登录WhatsApp Web
```

## 联系人操作

### 搜索联系人

```
搜索联系人"张三"
```

```
请查找我的联系人列表中的"李四"
```

### 查看联系人信息

```
查看联系人"张三"的详细信息
```

```
打开与"李四"的聊天，并查看他的个人资料
```

## 消息操作

### 发送消息

```
给"张三"发送一条消息："你好，今天下午3点有时间开会吗？"
```

```
请向"公司群组"发送消息："各位好，明天上午10点开例会，请准时参加"
```

### 查看消息

```
查看与"张三"的最近对话
```

```
打开"家庭群组"，阅读最近20条消息
```

```
查找与"李四"的对话中包含关键词"会议"的消息
```

### 回复消息

```
回复"张三"最后一条消息："好的，没问题"
```

```
在"项目组"群中回复关于预算的讨论："预算已经增加到10万元"
```

## 多媒体操作

### 发送图片

```
从我的电脑选择一张图片发送给"张三"
```

```
将桌面上的"产品设计.jpg"发送到"设计团队"群组
```

### 发送文件

```
发送一个PDF文件给"李四"
```

```
将"项目计划.docx"发送给"项目组"
```

## 群组操作

### 创建群组

```
创建一个新的群组，命名为"周末活动"，添加"张三"、"李四"和"王五"
```

### 群组设置

```
将"项目组"群的描述修改为"2023年Q2项目讨论组"
```

```
将我在"家庭群组"中的昵称改为"大哥"
```

## 高级操作

### 搜索特定日期的消息

```
查找与"张三"在2023年5月15日的所有对话
```

### 导出聊天记录

```
导出与"李四"的完整聊天记录
```

### 静音通知

```
将"公司群组"静音8小时
```

## 注意事项

- 某些操作可能需要您在浏览器中手动确认
- 如果您使用的是无头模式，部分需要用户交互的操作可能无法完成
- 发送敏感信息前，请确保您的API密钥和通信渠道安全
- 在群组中的操作可能受到您的权限限制（如您是否为管理员） 