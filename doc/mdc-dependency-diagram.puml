@startuml MDC依赖关系图

' MDC依赖关系架构图
skinparam {
  BackgroundColor white
  ArrowColor #5ea9ff
  ComponentBackgroundColor #f0f9ff
}

title fs-paas-appframework 单元测试MDC依赖关系

' 定义核心组件
component "@301-appframework-base-unit-demo.mdc\n基础单元测试规范" as BaseDemo
component "@301-appframework-bussiness.mdc\nAction/Controller类规范" as BusinessMDC
component "@300-generate-unit-test.mdc\n通用类规范" as GeneralMDC

' 定义依赖关系
BusinessMDC --> BaseDemo : 依赖
GeneralMDC --> BaseDemo : 依赖
BusinessMDC ..> GeneralMDC : 特定情况下转用

note bottom of BaseDemo
  提供所有单元测试所需对象的示例结构
end note

note bottom of BusinessMDC
  处理Action/Controller类且调用父类方法时
end note

note bottom of GeneralMDC
  处理非Action/Controller类
  或不调用父类方法的Action/Controller类
end note

@enduml 