# WhatsApp MCP服务问题排查指南

本文档提供了使用WhatsApp MCP服务时可能遇到的常见问题及其解决方案。

## 启动问题

### 服务无法启动

**问题描述**: 运行启动脚本时，服务无法正常启动。

**可能的解决方案**:
1. 确认已安装所有依赖：`pip install -r requirements.txt`
2. 确认已安装uv工具：`pip install uv`
3. 确认已安装mcp-server-browser-use：`uv pip install mcp-server-browser-use`
4. 检查是否提供了正确的API密钥
5. 尝试使用更明确的路径：`python /完整路径/start_mcp_service.py`

### Chrome浏览器问题

**问题描述**: 服务启动时报告找不到Chrome浏览器。

**可能的解决方案**:
1. 确认Chrome浏览器已正确安装
2. 使用`--chrome-path`参数指定Chrome浏览器的完整路径
3. 关闭所有正在运行的Chrome实例后再启动服务

## 连接问题

### Claude Desktop无法连接MCP服务

**问题描述**: Claude Desktop显示无法连接到MCP服务。

**可能的解决方案**:
1. 确认MCP服务已经启动
2. 检查Claude Desktop的配置文件是否正确
3. 尝试重启Claude Desktop
4. 检查是否有防火墙或安全软件阻止了连接

### WhatsApp Web无法登录

**问题描述**: 服务无法正常打开WhatsApp Web或无法登录。

**可能的解决方案**:
1. 确认您的网络连接正常
2. 尝试在正常浏览器中访问 https://web.whatsapp.com 是否可以成功登录
3. 如果使用无头模式 (`--headless`), 请关闭该选项尝试
4. 检查是否需要更新Chrome浏览器版本

## 功能问题

### 视觉功能不工作

**问题描述**: MCP服务无法正确分析屏幕内容或执行视觉相关操作。

**可能的解决方案**:
1. 确认已设置`MCP_USE_VISION=true`
2. 检查是否使用了支持视觉功能的模型（如Claude 3.5 Sonnet）
3. 尝试使用非无头模式启动服务
4. 确保屏幕分辨率设置正确

### 消息无法发送

**问题描述**: 通过MCP服务无法成功发送WhatsApp消息。

**可能的解决方案**:
1. 确认您已成功登录WhatsApp Web
2. 检查联系人名称是否正确
3. 尝试通过完整的电话号码发送（包括国家代码）
4. 检查是否有网络连接问题
5. 观察浏览器中是否有需要确认的弹窗

### 搜索功能不准确

**问题描述**: 搜索联系人或消息不返回预期结果。

**可能的解决方案**:
1. 使用更精确的搜索词
2. 确保联系人名称拼写正确
3. 对于中文或特殊字符，可能需要使用转义字符
4. 尝试使用电话号码而非名称进行搜索

## API密钥问题

### API密钥错误

**问题描述**: 服务报告API密钥错误或认证失败。

**可能的解决方案**:
1. 确认API密钥正确且未过期
2. 检查API密钥权限是否足够
3. 确认环境变量正确设置或通过`--api-key`参数提供
4. 对于不同的提供商，确保设置了正确的端点

## 浏览器自动化问题

### 元素无法找到

**问题描述**: MCP服务报告无法找到网页元素或按钮。

**可能的解决方案**:
1. 确认WhatsApp Web界面加载完成
2. 检查WhatsApp界面是否有更新导致元素选择器失效
3. 尝试使用更明确的指令，如"点击页面右上角的'菜单'按钮"
4. 等待几秒钟让页面完全加载后再尝试操作

### 自动化操作太慢

**问题描述**: MCP服务执行操作的速度很慢。

**可能的解决方案**:
1. 检查网络连接速度
2. 考虑关闭无头模式以便于调试
3. 减少同时运行的应用程序数量
4. 增加系统资源（内存、CPU）

## 高级问题排查

如果上述解决方案无法解决您的问题，您可以尝试以下高级排查方法：

1. **启用详细日志**：设置环境变量`BROWSER_USE_LOGGING_LEVEL=debug`获取更详细的日志信息
2. **检查浏览器跟踪**：查看`trace.json`文件了解浏览器操作详情
3. **检查录制**：如果启用了录制功能，查看`recording.mp4`了解浏览器行为
4. **使用开发者工具**：在非无头模式下，使用Chrome开发者工具检查页面元素
5. **查看源代码**：如果您熟悉Python和浏览器自动化，可以查看`mcp-server-browser-use`源代码了解更多细节 