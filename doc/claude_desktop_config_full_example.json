{"mcpServers": {"browser-use": {"command": "uvx", "args": ["mcp-server-browser-use"], "env": {"OPENAI_API_KEY": "", "OPENAI_ENDPOINT": "https://api.openai.com/v1", "ANTHROPIC_ENDPOINT": "https://api.anthropic.com", "ANTHROPIC_API_KEY": "your_anthropic_api_key_here", "GOOGLE_API_KEY": "", "AZURE_OPENAI_ENDPOINT": "", "AZURE_OPENAI_API_KEY": "", "DEEPSEEK_ENDPOINT": "https://api.deepseek.com", "DEEPSEEK_API_KEY": "", "MISTRAL_API_KEY": "", "MISTRAL_ENDPOINT": "https://api.mistral.ai/v1", "OLLAMA_ENDPOINT": "http://localhost:11434", "ANONYMIZED_TELEMETRY": "true", "BROWSER_USE_LOGGING_LEVEL": "info", "CHROME_PATH": "", "CHROME_USER_DATA": "", "CHROME_DEBUGGING_PORT": "9222", "CHROME_DEBUGGING_HOST": "localhost", "CHROME_PERSISTENT_SESSION": "false", "BROWSER_HEADLESS": "false", "BROWSER_CHROME_INSTANCE_PATH": "", "BROWSER_DISABLE_SECURITY": "false", "BROWSER_WINDOW_WIDTH": "1280", "BROWSER_WINDOW_HEIGHT": "720", "BROWSER_TRACE_PATH": "trace.json", "BROWSER_RECORDING_PATH": "recording.mp4", "RESOLUTION": "1920x1080x24", "RESOLUTION_WIDTH": "1920", "RESOLUTION_HEIGHT": "1080", "VNC_PASSWORD": "your_vnc_password_here", "MCP_MODEL_PROVIDER": "anthropic", "MCP_MODEL_NAME": "claude-3-5-sonnet-20241022", "MCP_TEMPERATURE": "0.3", "MCP_MAX_STEPS": "30", "MCP_USE_VISION": "true", "MCP_MAX_ACTIONS_PER_STEP": "5", "MCP_TOOL_CALL_IN_CONTENT": "true"}}}}