#!/usr/bin/env python3
import os
import sys
import subprocess
import argparse

def check_dependencies():
    """检查必要的依赖是否已安装"""
    try:
        import requests
        import selenium
        import qrcode
        from PIL import Image
        return True
    except ImportError as e:
        print(f"缺少必要的依赖: {e}")
        print("请运行 'pip install -r requirements.txt' 安装所需依赖")
        return False

def check_chrome():
    """检查Chrome浏览器是否安装"""
    if sys.platform == 'darwin':  # macOS
        chrome_paths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            os.path.expanduser('~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome')
        ]
    elif sys.platform == 'win32':  # Windows
        chrome_paths = [
            os.path.expandvars(r'%ProgramFiles%\Google\Chrome\Application\chrome.exe'),
            os.path.expandvars(r'%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe'),
            os.path.expandvars(r'%LocalAppData%\Google\Chrome\Application\chrome.exe')
        ]
    else:  # Linux 和其他系统
        chrome_paths = [
            '/usr/bin/google-chrome',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium'
        ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            return path
    
    print("未找到Chrome浏览器，请确保Chrome已安装")
    return None

def check_uv():
    """检查uv是否已安装"""
    try:
        subprocess.run(['uv', '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("未找到uv工具，请运行 'pip install uv' 进行安装")
        return False

def start_mcp_service(args):
    """启动MCP服务"""
    env = os.environ.copy()
    
    # 设置必要的环境变量
    if args.api_key:
        env['ANTHROPIC_API_KEY'] = args.api_key
    
    if args.chrome_path:
        env['CHROME_PATH'] = args.chrome_path
    
    env['BROWSER_HEADLESS'] = str(args.headless).lower()
    env['MCP_MODEL_PROVIDER'] = args.provider
    env['MCP_MODEL_NAME'] = args.model
    env['MCP_USE_VISION'] = str(args.vision).lower()
    
    # 构建启动命令
    cmd = ['uvx', 'mcp-server-browser-use']
    
    print("启动MCP服务...")
    print(f"使用模型提供商: {args.provider}")
    print(f"使用模型: {args.model}")
    print(f"是否使用无头模式: {args.headless}")
    print(f"是否开启视觉功能: {args.vision}")
    
    try:
        subprocess.run(cmd, env=env)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动服务时发生错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='启动WhatsApp MCP服务')
    parser.add_argument('--api-key', help='Anthropic API密钥')
    parser.add_argument('--chrome-path', help='Chrome浏览器路径')
    parser.add_argument('--headless', action='store_true', help='使用无头模式（不显示浏览器界面）')
    parser.add_argument('--provider', default='anthropic', help='模型提供商 (默认: anthropic)')
    parser.add_argument('--model', default='claude-3-5-sonnet-20241022', help='使用的模型 (默认: claude-3-5-sonnet-20241022)')
    parser.add_argument('--vision', action='store_true', default=True, help='启用视觉功能')
    
    args = parser.parse_args()
    
    # 检查依赖和环境
    if not check_dependencies():
        return
    
    if not check_uv():
        return
    
    chrome_path = args.chrome_path or check_chrome()
    if not chrome_path:
        return
    
    if not args.chrome_path:
        args.chrome_path = chrome_path
    
    # 强制检查 API 密钥
    if not args.api_key and 'ANTHROPIC_API_KEY' not in os.environ:
        print("错误: 未提供Anthropic API密钥，MCP服务无法启动")
        print("请通过以下方式之一提供API密钥:")
        print("1. 使用命令行参数: python start_mcp_service.py --api-key=你的API密钥")
        print("2. 设置环境变量: export ANTHROPIC_API_KEY=你的API密钥")
        print("3. 在claude_desktop_config.json文件中添加API密钥")
        return
    
    # 启动服务
    start_mcp_service(args)

if __name__ == "__main__":
    main() 