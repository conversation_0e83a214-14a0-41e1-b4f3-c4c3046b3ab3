#!/usr/bin/env python3
"""
DeepSeek API 使用示例
使用最新版本的openai库调用DeepSeek API
"""

from openai import OpenAI
import os

def main():
    # 从环境变量获取API密钥和代理设置
    api_key = os.getenv("DEEPSEEK_API_KEY")
    proxy = os.getenv("DEEPSEEK_PROXY")
    
    if not api_key:
        print("❌ 错误: 请设置环境变量 DEEPSEEK_API_KEY")
        return
    
    # 创建OpenAI客户端，指向DeepSeek的API
    client = OpenAI(
        api_key=api_key,
        base_url=proxy or "https://api.deepseek.com"  # DeepSeek的API端点
    )
    
    try:
        print("🤖 正在调用DeepSeek API...")
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello"}
            ],
            stream=False
        )
        
        print("✅ API调用成功!")
        print(f"回复: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")

if __name__ == "__main__":
    main() 