# WhatsApp MCP服务接入项目

## 项目简介

本项目旨在接入Model Context Protocol (MCP)服务，通过浏览器自动化技术，实现与WhatsApp的交互功能。该项目基于[browser-use MCP服务](https://github.com/Saik0s/mcp-browser-use)，使用AI驱动的浏览器自动化来实现WhatsApp的消息收发与管理。

## 功能特点

- 🌐 **浏览器自动化** - 实现WhatsApp网页版的自动化操作
- 💬 **消息管理** - 查询、发送和管理WhatsApp消息
- 👥 **联系人管理** - 搜索和查看联系人信息
- 🔄 **会话持久化** - 保持浏览器会话，持续提供服务
- 🧠 **AI集成** - 通过MCP协议与AI模型交互

## 环境要求

- Python 3.11或更高版本
- Chrome/Chromium浏览器
- uv (Python包管理工具)
- Anthropic API密钥（必需）

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 安装uv工具：
```bash
pip install uv
```

3. 安装MCP服务器：
```bash
uv pip install mcp-server-browser-use
```

4. 确保Chrome浏览器已安装

5. 配置Claude Desktop或其他支持MCP的客户端

## API密钥设置（必需）

要使用本项目，您**必须**有有效的Anthropic API密钥。可以通过以下三种方式之一配置API密钥：

1. **环境变量设置**：
```bash
export ANTHROPIC_API_KEY=您的API密钥
```

2. **命令行参数**：
```bash
python start_mcp_service.py --api-key=您的API密钥
```

3. **配置文件设置**：在Claude Desktop配置文件中添加API密钥
```json
"env": {
  "ANTHROPIC_API_KEY": "您的API密钥",
  "BROWSER_HEADLESS": "false",
  ...
}
```

> **注意**：如果未设置API密钥，MCP服务将无法启动，并且会显示错误消息"Initial server discovery failed"。

### Claude Desktop配置

在Claude Desktop中的配置文件中添加以下内容：

MacOS: `~/Library/Application\ Support/Claude/claude_desktop_config.json`
Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
"mcpServers": {
    "browser-use": {
      "command": "uvx",
      "args": [
        "mcp-server-browser-use",
      ],
      "env": {
        "ANTHROPIC_API_KEY": "您的API密钥",
        "BROWSER_HEADLESS": "false",
        "MCP_MODEL_PROVIDER": "anthropic",
        "MCP_MODEL_NAME": "claude-3-5-sonnet-20241022",
        "MCP_USE_VISION": "true"
      }
    }
}
```

## 使用方法

1. 启动MCP服务
2. 在Claude Desktop中连接MCP服务
3. 通过自然语言指令操作WhatsApp网页版
4. 示例指令：
   - "搜索联系人张三"
   - "查看最近的WhatsApp消息"
   - "给李四发送一条消息：'你好，我们下午3点开会'"

## 项目文件结构

- `requirements.txt`: 项目依赖
- `pythonTool/`: Python工具集
- `doc/`: 项目文档
- `popup/`: 弹窗界面相关代码
- `tool/`: 实用工具
- `doc/arthas使用指南.md`: Arthas Java诊断工具详细使用文档

## 注意事项

- 首次使用时需要在浏览器中手动登录WhatsApp Web
- 确保网络连接稳定
- API密钥需要自行申请并妥善保管
- 使用时请遵循WhatsApp的使用条款和隐私政策

## 问题排查

- **浏览器冲突**: 启动前关闭所有Chrome实例
- **API错误**: 验证环境变量中的API密钥是否正确
- **视觉支持**: 确保设置了`MCP_USE_VISION=true`以启用屏幕截图分析
- **未连接**: 如果遇到连接问题，检查您是否已经登录WhatsApp Web
- **Java应用问题**: 对于Java应用的问题诊断，请参考`doc/arthas使用指南.md`

## 许可证

MIT许可证

## Java应用诊断工具

本项目中添加了Arthas使用指南，这是阿里巴巴开源的强大Java诊断工具。通过Arthas，您可以：

- 排查Java应用的CPU高负载问题
- 分析方法执行耗时
- 查看内存使用情况
- 热修复线上代码

详细指南请查看 [Arthas使用指南](doc/arthas使用指南.md)
