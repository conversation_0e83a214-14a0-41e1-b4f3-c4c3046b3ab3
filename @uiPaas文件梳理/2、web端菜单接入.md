# 2、web端菜单接入

## 一、背景

纷享的菜单的需求比较多，比如代理通菜单、厂商门户菜单、CRM菜单等等，UI PAAS侧对菜单的设计器做了统一的封装，业务侧只需要引入前端的框架，并配置相关的配置文件，即可实现菜单管理后台->用户侧的菜单页面交互，业务侧只需要开发自己的组件即可。

## 二、接入

### 1、前端接入
[业务组件接入（适用于web端整体和移动端设计器部分）](http://wiki.firstshare.cn/pages/viewpage.action?pageId=128633929)

### 2、服务端接入配置

#### （1）、相关定义

##### 1、menu定义：
[13）UI PaaS外部接入 - 2、菜单元数据字段含义（fs-webpage-customer-menus）](13）UI-PaaS外部接入.md#2菜单元数据字段含义fs-webpage-customer-menus)

##### 2、menu-collection定义
[13）UI PaaS外部接入 - 3、菜单collection字段含义（fs-webpage-customer-menus-collection）](13）UI-PaaS外部接入.md#3菜单collection字段含义fs-webpage-customer-menus-collection)

#### （2）、接入方式

##### 第一步：注册应用（如果只是使用设计器，可以不进行注册）
**注册方式：**

**1、修改配置文件**：**fs-webpage-app-potrol-template**

**备注：注册请更新wiki：**http://wiki.firstshare.cn/pages/viewpage.action?pageId=148373433

---

**example：**

```
// fs-webpage-app-potrol-template
{
  "FSAID_11491084":{	//appId
    "defaultName":"厂商门户",		//应用的名称
    "hasMenu":true,		//是否包含菜单
    "crossApp":true,		//是否是互联
	"nameI18nKey":""		//应用名称的多语key
  }
}
```

##### 第二步：注册菜单（如果是新的菜单的话，需要先注册菜单）
对于预置对象, 想要接入CRM应用, 只需在配置文件[fs-webpage-customer-preObject](https://oss.firstshare.cn/cms/?search=fs-webpage-customer-preObject)注册即可

**注册方式：**

**1、修改配置文件**：**fs-webpage-customer-menus（菜单注册）**

---

**example：**

**菜单注册：**

```
// menu示例
{
  "deviceTypes": [		//支持的端
    "iOS",
    "Android",
    "WEB",
    "Desktop_Mac",
    "Desktop_Windows"
  ],
  "personPrivilege": {	//个人权限
    "functionCode": [
      "List"
    ]
  },
  "name": "公海",	//菜单名称
  "iconIndex": 452,
  "icon": {			
    "icon_2": "https://a9.fspage.com/FSR/fs-qixin/static/objicon/faceicon/gonghai.png",
    "icon_1": "A_201907_02_3d067987f2b640b0b2d36f16b96a8ffb.svg"
  },
  "nameI18nKey": "so.menu.item.HighSeasObj",		//多语的key
  "id": "HighSeasObj",		//菜单的id
  "url": {		//菜单的url，为true，表示要使用服务端下发的url
    "useServerUrl": false,
    "webUrl": "fs://CRM/objectList/HighSeasObj?{\"apiName\":\"HighSeasObj\",\"handlerSelector\":\"pushListVC:\",\"pushListVC\":\"HighSeasObj\"}"
  }
}
```

##### icon说明: 对于来自配置文件的菜单前端**只会**读取 iconIndex 字段值, 对应的icon是 [fx-icon](https://fe.firstshare.cn/fx-icon/#/) 中的图片, 比如示例中的 "iconIndex": 452 就是 <span class="fx-icon-obj-app453"></span> (库里编号从1开始)

![image2025-5-20_19-44-20.png]

##### iconIndex

##### 第三步：注册collection（如果已经注册过collection，不需要再建立新的collection，只需要在原collection下加组件即可）

**注册方式：**

**1、修改配置文件：fs-webpage-customer-menus-collection**

---

**example：**

**collection注册：**

```
// collection示例
[ 
  {
    "tenantPrivilege": {
      "appId": "FSAID_11490d9e"
    },
    "name": "代理通其他",
    "nameI18nKey": "",
    "menuSourceType": 1,
    "id": "dailitongOther",
    "menus": [
      "LeadsPoolObj",
      "HighSeasObj"
    ]
  },
  {
    "tenantPrivilege": {
      "appId": "FSAID_11490d9e"		//互联必须指定对应的应用appId
    },
    "name": "代理通对象",
    "nameI18nKey": "",
    "menuSourceType": 2,		//1:Config配置, 2:对象, 4:自定义页面, 6: 自定义菜单项
    "id": "dailitongCrossObj",
    "type": "all"		//all、不进行过滤；preObject、过滤出预置对象；customerObject、过滤出自定义对象; 如果来源是对象,此字段不可缺失
  }
]
```

##### 第四步：新建设计器管理的配置（如果已经存在，则不需要处理）

**配置方式：**

**1、新建配置文件：业务侧自己定义配置名称**

**example：fs-webpage-customer-web-menu-vendor-component-list**

**设计器管理配置**

```
// fs-webpage-customer-web-menu-vendor-component-list
[
  {
    "componentType": 1,		//1、分组
    "id": "dailitongObjPId",	//分组id
    "title": "代理通对象",	//分组name
    "titleI18nKey": ""		//分组多语
  },
  {
    "componentType": 2,		//1、collection（也为分组）
    "id": "dailitongObj",		//分组id
    "title": "代理通对象",	//分组name
    "titleI18nKey": "",		//分组多语
    "collectionId": "dailitongCrossObj",	//collectionId
    "parentId": "dailitongObjPId"		//父节点
  },
  {
    "componentType": 1,
    "id": "dailitongOtherPId",
    "title": "代理通其他",
    "titleI18nKey": ""
  },
  {
    "componentType": 2,
    "id": "dailitongOther",
    "title": "代理通其他",
    "titleI18nKey": "",
    "collectionId": "dailitongOther",
    "parentId": "dailitongOtherPId"
  },
  {
    "componentType": 1,
    "id": "fuwutongObjPId",
    "title": "服务通对象",
    "titleI18nKey": ""
  },
  {
    "componentType": 2,
    "id": "fuwutongObjPId",
    "title": "服务通对象",
    "titleI18nKey": "",
    "collectionId": "fuwutongCrossObj",
    "parentId": "fuwutongObjPId"
  },
  {
    "componentType": 1,
    "id": "dinghuotongObjPId",
    "title": "订货通对象",
    "titleI18nKey": ""
  },
  {
    "componentType": 2,
    "id": "dinghuotongObjPId",
    "title": "订货通对象",
    "titleI18nKey": "",
    "collectionId": "dinghuotongCrossObj",
    "parentId": "dinghuotongObjPId"
  },
  {
    "componentType": 1,
    "id": "otherPId",
    "title": "其他",
    "titleI18nKey": ""
  },
  {
    "componentType": 2,
    "id": "otherVendorCollection",
    "title": "其他",
    "titleI18nKey": "",
    "collectionId": "otherVendorCollection",
    "parentId": "otherPId"
  },
  {
    "componentType": 1,
    "id": "eserviceManagementPId",
    "title": "服务管理",
    "titleI18nKey": ""
  },
  {
    "componentType": 2,
    "id": "eservice_management",
    "title": "服务管理",
    "titleI18nKey": "",
    "collectionId": "eservice_management",
    "parentId": "eserviceManagementPId"
  }
]
```

##### 第五步：将业务侧自定义的设计器管理配置注册到UI PAAS的管理配置文件中

**注意:** CRM应用有新老两个版本, 新版本: 管理后台 → 应用列表→ CRM → 编辑 除了基本信息, 还有web应用视图和移动应用视图, 老版本没有, 以下提到的appId, 对于老版本CRM, 是 "CRM", 对于新版本CRM, 是 "paasCRM" 

**注册方式：**

**1、修改配置文件：fs-webpage-customer-web-component-list-map**

**key的定义方式：appId-web-menu（appId是应用的id）**

---

**example：**

```
// 管理组件示例
{
  "FSAID_11491009-web-menu": "fs-webpage-customer-web-menu-vendor-component-list"
}
```

##### 第六步：将第二步新注册的菜单配置到fs-webpage-menu-config中，如果不进行配置，则在用户态和管理态无法展示菜单

**配置方式：**

**这个配置文件中配置的菜单一定要添加在设计器中，设计器中没有的话showMenu下的菜单会默认追加（有很多人漏配置引起线上问题）**

**1、修改配置文件：fs-webpage-menu-config**

**key的定义方式：showMenu-appId（appId是应用的id, 注意区分新老CRM）**

**value：注册的菜单id**

---

**example：**

```
// 设置菜单显示
showMenu-FSAID_11490f80=LeadsPoolObj,HighSeasObj
```

## 三、菜单灰度

在大版本上线的时候，业务侧在增加菜单的时候希望按企业做灰度，UI PAAS侧统一支持对菜单按企业做灰度

### 灰度配置方式：

注意部分组件或者collection是web和App公用的，要添加灰度或者放开入口，需要考虑是否两端都有影响(app默认是放开的，需要灰度参考：[3、移动端组件接入-三、组件灰度](3、移动端组件接入.md#三组件灰度))

#### 第一步：加要灰度的菜单加入到菜单灰度配置（fs-webpage-menu-config）中
appId注意区分新老CRM

**格式：**
```
grayMenuApiName-appId=id
```

#### 第二步：配置按企业灰度的配置（gray-rel-webPage）

**格式：**
```
id-enterprise=deny;71574;
```

**example：**

**fs-webpage-menu-config**

```
grayMenuApiName-CRM=ParamReview,Fmcg_store__c,ClosedStoreReview
```

**gray-rel-webPage**

**后续优先使用[fs-gray-webPage](https://oss.foneshare.cn/)**

```
ParamReview-enterprise=deny;670624;590268;608158;
Fmcg_store__c-enterprise=deny;590268;590103;590056;
ClosedStoreReview-enterprise=deny;608158;670624;590251;701209;
```

## 四、配置技巧（针对想增加菜单的同学）

[菜单配置流程]

## 五、Q&A

## 六、高阶能力

### 1、支持预设分组菜单

**配置文件**：fs-webpage-customer-group

**example：**

```
// 预设分组菜单
[
	{
		"groupName":"项目管理",		//想添加的分组名称, 
		"groupApiName":"group_theProjectManagement",	//分组apiName
		"afterType":"group",	//追加菜单类型：group，追加到某个分组后边，menu：追加到某个菜单后边
		"afterAppId":"CRM",		//追加菜单的appId
		"appId":"CRM",		//分组的appId
		"menuAppId":"CRM",	//当前分组下菜单的appId	
		"menus":[		// 菜单的apiName集合
			"ProjectObj",
			"ProjectStageObj",
			"ProjectTaskObj",
			"TimeSheetObj",
			"DependenciesObj"
		]
	}
]
``` 