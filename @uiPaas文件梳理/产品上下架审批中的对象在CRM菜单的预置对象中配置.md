# 产品上下架审批中的对象在CRM菜单的预置对象中配置

## 很简单哈，一分钟搞定

## 上架操作：

请修改配置文件：[fs-webpage-customer-preObject](https://oss.firstshare.cn/cms/edit/config/14635)

修改动作：

在文件追加以下内容：

```json
{
  "apiName": "ProductConstraintObj",
  "hidden": true, //新增对象一定要配置成true呀，不然老的菜单配置里也会显示该对象，大企业会提bug的
  "icon": {}
},
```

参考修改截图：

![image2024-7-29_11-8-49.png]

## 下架操作：

请修改配置文件：[fs-webpage-customer-preObject](https://oss.firstshare.cn/cms/edit/config/14635)

修改动作：

在文件删除对应的对象信息配置，直接删除就行：

参考修改截图：

![image2024-7-29_11-9-50.png] 