# 多语言逻辑梳理及最新方案

## 优化背景：

1. 925紧急支持UIPaas侧预设多语的能力（取模版的预设多语值）
2. 对象侧取多语逻辑改造，支持在后台改名时，只影响当前语种，不影响其他语种的值

- 925之前多语展示逻辑：
  - 对象侧：
    - 下发：customkey>preKey>默认值
    - 同步：如果customKey有值（翻译工作台翻译过）并且当前修改的值与customKey中的当前语言值不一致，才同步多语
  - UIPaas：
    - 下发：customKey>preKey>默认值
    - 同步：如果customKey有值（翻译工作台翻译过）并且当前修改的值与customKey中的当前语言值不一致，才同步多语
    - 问题：约定的逻辑是这样的，但是实际到不同的业务侧，处理的逻辑不一致并且有缺失（比如：菜单项名称缺少同步逻辑（改名不生效），自定义页面组件名称有缓存的逻辑（改名生效后又失效的问题））

- 存在的问题：
  - 对象+Uiaas：
    - 当用户没有翻译过，改值了以后，则多语不会同步，所有语种显示同一个值（产品希望在新建编辑页改名，只会改当前语种的值，不会影响其余语种的值）
    - 历史上数据无法判断是否被更改过，下发多语时容易造成覆盖客户语言的问题
  - UIPaas：
    - 部分场景在处理中文语种以外的其他语种下保存过后，预设语言会失效（菜单分组）
    - 多语不支持跟随模版的多语翻译（业务侧预设了模版以后，无法支持预设翻译），原因：UIPaas侧之前的key中包含了企业id，导致跨租户取多语取不到
    - 沙盒拷贝多语拷贝失效，原因：UIPaas侧之前的key中包含了企业id，导致跨租户取多语取不到
    - 历史上存在多个key的情况，取值逻辑不清晰

- 优化的目标：
  - 在后台改名，改名的值只针对当前语种生效，不影响其他语种的值
  - 历史上改过名字的，预设多语可以正常显示（针对用户没有改过名称，只是在其他语种保存过的情况）
  - 支持模版企业的预设多语翻译
  - 沙盒拷贝支持多语名称
  - 在支持预设多语的基础上，尽量减少对已有多语的影响

## 方案迭代过程：

开发过程中遇到的问题及方案改动过程：

### 原始逻辑：
![image2024-12-17_15-11-37.png]

# 第一版方案：
该方案主要解决的问题是：前台、设计器、翻译工作台支持下发预设多语

- 下发逻辑与之前保持一致，自定义多语>预设多语>默认值
- 同步逻辑由最开始的判断自定义key有翻译值在同步，变为了有预设值并且当前更改值和预设多语当前语言值不一致，也同步多语（保证在设计器改了名称以后，只影响当前语种的语言，不影响其他语种）

### 解决的问题：
- 在设计器保存了以后，其余预设语种也会同步到自定义key上，可以保证变更名称时，只会影响当前语言值，其余语言还可以正常走预设多语

### 存在的问题：
- 历史数据同步没有处理到:
  - eg：菜单分组预设key的拼值逻辑为:group_中文名称，在英文环境下保存过以后，库里值变为了英文值，这个时候取多语就拼不出来该多语key，导致多语失效
- 下发数据时
  - 部分数据取不到预设多语的key:对象侧预设多语存储在布局中，uiPaas侧没有，导致部分组件取不到预设多语
  - 改过名称的组件会被预设多语覆盖（下发没有判断是否改过名称）
- 业务侧反馈要支持模版多语，但该方案无法支持（拼key逻辑问题，key中包含企业id）
- 实施侧反馈沙盒拷贝多语失效问题（拼key逻辑问题）

![image2024-12-17_11-21-35.png]

# 第二版方案：
## 解决的问题:
1. 业务侧预设的模版组件，页面名称等支持预设翻译，沙盒拷贝翻译正常生效
2. 解决用户改过名的组件被预设多语覆盖的问题
3. 解决历史上部分组件拼key逻辑缺陷，导致预设多语无法正常显示的问题

## 处理方案：
1. 定义处理多语的新结构：
```
{
	"customKey":""//自定义key
	"preKeyList":["",""]//预设多语集合
	"name":""//编辑时为最新更改的值，下发时为库里值
}

customKey:中放最新的自定义多语key（如果自定义key中带企业id，则吧企业id remove掉，并把带企业id的key放到preKeyList中(兼容历史数据)）

preKeyList:中放带企业id的key，历史上使用过的key，以及预设多语的key，该集合是有顺序的，当自定义key没值的时候，根据preKeyList的先后顺序给组件下发多语

name:如果是保存操作，则为用户最新更改的值，如果为下发接口，则为库里值
```

2. 针对历史拼key逻辑缺陷，预设多语无法正常显示（预设key包含中文值的情况）：渐渐将该值废弃，只做为保底取多语的逻辑，引导业务侧同步0企业的老key。eg:菜单分组
   1. 老key1：group_客户级商机管理，老key2：group_group-12312321324343sdasdas(group_+分组apiName)
   2. 推动业务侧同步老key2的拼key方式作为0企业词条，将所有的预设菜单分组0企业key同步完后，则老key1就废弃（逻辑中不会走到这块逻辑）
   3. **过度阶段走逻辑2，将老key1，和老key2都做为预设词条取值**

3. 针对key中带有企业id的问题，处理逻辑为：拼key时，如果自定义词条中有拼企业id，则将取出企业id作为最新的自定义key，将带有企业id的自定义老key，放入预设key中
   1. ![image2025-1-13_16-41-47.png]

4. 为解决用户改过名的组件被覆盖，并最大限度的使预设多语覆盖用户的词条做出以下优化
   1. preKeyList中放的是当前组件所有的历史词条+预设词条
   2. 在下发多语时，如果自定义key没值，则会将prekeyList中的词条拍平（根据词条的顺序，拿到每个语言下优先级最高的多语值），使用拍平后的预设多语值，跟name(库里存储的值)做contains比较
      1. 如果预设多语的某种语言和库里值存储的一致，代表当前用户，在历史上没有修改过该词条的值（解决在某种语言下编辑过以后，库里值变为编辑时的语言值，导致预设多语失效的问题），则直接下发当前的语言值
      2. 如果预设多语的任何语言和库里值都不一致，则代表当前用户改过该词条，则不下发预设多语，直接下发库里值

[翻译930逻辑]

### 还存在的问题：
1. 历史上的预设多语，存在只有英文值没有中文值的情况，导致下发多语时，判断为该类组件已经被改动过，没有正常下发预设多语（本质问题，preKeyList取多语时，如果有租户翻译的话，则不会取0企业翻译，导致用户曾经翻译过，并且没有翻译中文值的情况下，是否编辑过的判断会失效）

## **最新解决方案：**
改造参数结构
```
{
	"customKey":""//自定义key
 	"oldKeyList":["",""]//老多语集合 
    "preKeyList":[]//预设多语集合（0企业词条）
	"name":""//编辑时为最新更改的值，下发时为库里值
}

customKey:放最新的自定义多语key（如果自定义key中带企业id，则吧企业id remove掉，并把带企业id的key放到oldKeyList中(兼容历史数据)）

oldKeyList:放带企业id的keyList，历史上使用过的key,该集合是有顺序的，根据先后顺序取当前语言的多语，无需比较
	oldKeyList中包含带企业的key的原因：历史的设计缺陷，key中将企业id拼了进去(crmMenu.group.739043_9ed67a027b154498830449da9afafa2e.group-a1b1b7fe6590d67279)，影响：
		1.多语词条沙盒拷贝无法生效
		2.模版企业的预设词条多语取不到

preKeyList（0企业的key）:预设多语的keyList，当自定义key和OldKey没值的时候，将preKeyList中的多语拍平后与name值比较是否有重复，有重复则认为用户没改过，下发多语，没重复认为改过，下发name值

name（db中存储的值）:如果是保存操作，则为用户最新更改的值，如果为下发接口，则为库里值
```

![image2025-1-13_19-24-5.png]

![image2025-1-13_19-27-51.png]

## 相关反思：
1. 在方案前期设计时，对历史场景分析不足：我们认为的0企业词条，在历史上有座位租户词条使用过的情况
2. 方案设计时，对老企业key和预设key的概念模糊，将不一样的取key场景混在一起处理，导致取值错误

针对以上问题：
1. 在以后的方案设计和评估中，要先调研历史场景，是否符合预期，并将调研的结果写明在技术方案中，避免出现历史场景遗漏的问题
2. 明确概念界限，避免出现类似的概念模糊，混杂处理的问题。

## 多语待办：
- [x] 扫描模板库线上数据是否与多语平台的0租户词条不一致
  - [x] 扫描比对完成，比较模版中的菜单分组和多语平台上已有的0企业菜单分组key的中文值。未发现有数据不一致的情况
- [x] 菜单分组的修改为多语新逻辑跟随940发布，加灰度逐步扩灰
  - [ ] fktest内测企业+性能云企业+H5集成回归企业
  - [ ] 正式企业-小微/标准1738家+自申请6家
  - [ ] 正式企业-小微/标准343家+重要TOP活跃358家+自申请4家
- [ ] 其余业务改动跟随945上线

菜单分组验证点：

影响点：
1. 老crm菜单分组
2. 新crm菜单分组
3. 互联引用菜单分组

#### 有灰度
| 语言环境 | dbValue | customKey（新key） | oldKey1(group_apiName)（老key1） | oldKey2(group_name)（老key2） | preKey(预设key 0租户的) | | result |
|---------|---------|-------------------|--------------------------------|-----------------------------|-----------------------|---|--------|
| en | 测试分组翻译 | en:testGroupTrans | en:testGroupTrans2222 | en:无 |  无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:testGroupTrans | en:无 | 无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:testGroupTrans | en:testGroupTrans2 | 无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:无 | en:testGroupTrans | 无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:无 | en:无 | en:testGroupTrans<br>zh_CN:测试分组翻译 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:无 | en:无 | en:testGroupTrans<br>zh_CN:测试分组 | | 测试分组翻译 |

#### 没灰度
| 语言环境 | dbValue | customKey（新key） | oldKey1(group_apiName)（老key1） | oldKey2(group_name)（老key2） | preKey(预设key 0租户的) | | result |
|---------|---------|-------------------|--------------------------------|-----------------------------|-----------------------|---|--------|
| en | 测试分组翻译 | en:testGroupTrans | en:testGroupTrans2222 | en:无 | 无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:testGroupTrans | zh_cn:测试分组翻译 | 无 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:testGroupTrans | en:testGroupTrans222 | 无 | | 测试分组翻译 |
| en | 测试分组翻译 | 无翻译 | en:无 | en:testGroupTrans | en:testGroupTrans222<br>zh_CN:测试分组翻译 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en: 无 | en:无 | en:testGroupTrans<br>zh_CN:测试分组翻译 | | testGroupTrans |
| en | 测试分组翻译 | 无翻译 | en:无 | en:无 | en:testGroupTrans<br>zh_CN:测试分组 | | 测试分组翻译 |

## 多语言上线检查步骤:
| 步骤 | 描述 | 预期 | |
|-----|------|-----|---|
| 1.检查配置/代码是否生效 | 目前支持多语有两种：<br>1.轻量，代码已经支持，只是配置没有放开<br>2.重量，需要代码级的改动 | 在翻译工作台可以漏出，翻译工作台翻译可以生效，设计器编辑可以反写，前后台多语正常下发 | |
| 2.检查是否影响未开启多语的企业 | 针对开启多语的企业，翻译了以后或者有预设多语以后，可以显示多语，未开启多语license的企业，不应该受到任何影响 | 未开启多语的企业，在设计器编辑可以正常提交和下发，历史已经改过名字的值不会受到影响，正常下发 | |
| 3. 检查是否有预设的key不符合预期 | 预设key预期都应该是所有的语种都有翻译的，如果有部分词条没有翻译，可能会导致下发时多语错误 | 预设词条的多语都有值，尤其是中英繁 | |
| 4.代码检查是否有判断多语的license | 没有开启多语license，不需要走翻译的逻辑 | 没有开启多语license的企业，不受多语的任何影响 | | 