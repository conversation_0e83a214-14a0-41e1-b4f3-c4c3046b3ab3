# 平台应用预置系统库修改

## 1、示例：预置平台应用模板信息

```
appId: "FSAID_PaaS_b181609390435"
description: "启用代理通后，新增渠道代理管理和代理通，渠道代理管理为上游渠道经理使用的应用。--为预设模板（勿动）"
icon: "https://a9.fspage.com/FSR/fs-qixin/static/appicon/svg/icon0.svg"
iconIndex: ""
name: " 伙伴管理"

templeId: "540053_9cc251a1125a4c0ebd7a83a569340f34"
type: "web"
webMenuId: "540053_a047c8fa93bd43b1926fd5c858976720"
webPageId: "540053_8ec06fc1ba694a2398f576ceb3282dbf"

templeId: "540053_feef73e9df3e4bd0a053731d01b7af35"
type: "app-pagetemplate"
appPageId: "540053-7de7b75efdc146ceb19b4284fce83318"
```

## 2、示例：预置平台应用系统库配置信息

```
fs-webpage-paas-app-system-config //应用预置系统库
--------------------------------------
,
"prm_app":[
{
"apiName":"FSAID_PaaS_b181609390435",
"tenantId":540053
}
]
```

--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

```
fs-webpage-app-pagetemplate //应用下模板信息
--------------------------------------

"prm_app":{

"PAAS":{

"FSAID_PaaS_b181609390435":[

"540053_9cc251a1125a4c0ebd7a83a569340f34",

"540053_feef73e9df3e4bd0a053731d01b7af35"

]

}

},

fs-webpage-menu-pagetemplate //应用下模板用到的web菜单配置
-------------------------------------------

,

"prm_app":{

"PAAS":{

"FSAID_PaaS_b181609390435":[

"540053_a047c8fa93bd43b1926fd5c858976720"

]

}

}

fs-webpage-homePage-pagetemplate //应用下模板用到的web页面配置
-------------------------------------------

,

"prm_app":{

"PAAS":{

"FSAID_PaaS_b181609390435":[

"540053_8ec06fc1ba694a2398f576ceb3282dbf"

]

}

}


fs-user-extension-pagetemplate //应用下模板用到的App端页面菜单配置
-------------------------------------------
,
"prm_app":{
"PAAS":{
"FSAID_PaaS_b181609390435":[
"540053-7de7b75efdc146ceb19b4284fce83318"
]
}
} 