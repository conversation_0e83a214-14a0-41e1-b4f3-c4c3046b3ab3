# 背景和动机
本文档针对Firstshare管理后台菜单接入的文档进行分析和理解。该文档提供了如何在管理后台系统中添加新菜单、设置操作权限以及角色权限关联的详细指南。这些步骤对于产品功能扩展和企业后台管理权限控制至关重要。

现在我们需要基于上述理解，为新的CMS系统添加管理后台菜单入口。

# 关键挑战和分析
管理后台菜单的接入涉及多个方面：
1. 前端落地页面的开发与规范 - 业务方代码不需要迁移到管理后台主代码manage仓库，而是需要在路由组manage下注册
2. 系统库中菜单数据的预置 - 需要在系统库(-100或-101)中预置菜单数据
3. 权限管理与角色关联 - 菜单、操作权限和角色之间的关系配置
4. 灰度策略与多环境支持 - 特别区分foneshare和专属云环境
5. 多语言支持 - 遵循特定的命名规则以支持国际化

## 重要注意事项
1. 【-100】和【-101】的区别：
   - 【-100】是全网企业都使用的系统库
   - 【-101】是大版本灰度企业使用的系统库（如880的灰度企业走-101，885走-100）
   - 专属云全部都是用【-100】

2. 890版本后，权限和菜单的code命名规则变更：
   - 需要去掉斜杠"/"、"="、"-"，否则无法录入多语词条
   - 例如：`paasappsetting/=/mainnav` 改为 `paasappsetting_mainnav`

3. 灰度控制机制：
   - 通过license版本控制
   - 通过企业账号/ID控制
   - 整体展示菜单的关系: (supportVersionList || supportModuleList) && (supportEnterpriseAccounts || supportEnterpriseIds)

## 菜单数据结构详细说明
菜单数据结构中的关键字段及其要求：

| 字段名称 | 字段apiName | 是否必要 | 备注说明 |
|---------|------------|---------|---------|
| appId | appId | 是 | 固定值: "facishare-system" |
| 菜单编码 | menuCode | 是 | 必填且唯一，890版本后需避免使用"/"、"="、"-" |
| 菜单名称 | name | 是 | 多语言key需添加：management.menu.facishare-system.menuCode |
| 父菜单 | parentCode | 是 | 父级菜单menucode，一级菜单值为"00000000000000000000000000000000" |
| 菜单图标 | menuIcon | 否 | 一级菜单需要上传图标资源(20x20,40x40的png) |
| 菜单排序 | order | 是 | 同级菜单下的排序值 |
| 菜单路由配置 | route | 是 | 前端规范只支持全小写，CRM平台管理下菜单需以"crmmanage/=/"开头 |

示例JSON结构：
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "menuCode": "paasappsetting/=/mainnav",
  "menuIcon": "paasappsetting/=/mainnav", 
  "name": "Web端主导航",
  "order": 50,
  "parentCode": "business_customization",
  "route": "paasappsetting/=/mainnav"
}
```

## 操作权限数据结构详细说明
操作权限数据结构中的关键字段及其要求：

| 字段名称 | 字段apiName | 是否必要 | 备注说明 |
|---------|------------|---------|---------|
| appId | appId | 是 | 固定值: "facishare-system" |
| 功能编码 | functionCode | 是 | 必填且唯一，通常与menuCode保持一致 |
| 操作权限名称 | name | 是 | 多语言key：management.function.facishare-system.(name) |
| 父级code | parentCode | 是 | 通常值为"00000000000000000000000000000000" |
| 排序 | order | 否 | 功能权限项在列表中的排序 |
| 是否支持限定分管范围 | limitScope | 否 | 如部门新建操作权限，受分管范围限制，值为true |
| 菜单编码 | menuCode | 是 | 与对应菜单的menuCode保持一致 |

示例JSON结构：
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "functionCode": "paasappsetting/=/mainnav",
  "limitScope": false,
  "menuCode": "paasappsetting/=/mainnav",
  "name": "Web端主导航",
  "order": 80,
  "parentCode": "00000000000000000000000000000000"
}
```

## 角色-操作权限关系详细说明
角色-操作权限关系中的关键字段及其要求：

| 字段名称 | 字段apiName | 是否必要 | 备注说明 |
|---------|------------|---------|---------|
| appId | appId | 是 | 固定值: "facishare-system" |
| 功能编码列表 | functionCodes | 是 | 角色绑定的操作权限项列表 |
| 角色编码 | roleCode | 是 | 如系统管理员roleCode="99"(默认具备所有操作权限) |

常用的预置角色code：
- 系统管理员：roleCode="99"
- 组织架构管理员：roleCode="47"
- CRM管理员：roleCode="31"
- 互联企业管理员：roleCode="60"
- 应用中心管理员：roleCode="101"
- 企业日志监察员：roleCode="103"
- 工作观察者：roleCode="48"

示例JSON结构：
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "functionCodes": [
    "paasappsetting/=/mainnav",
    "paaSApplication"
  ],
  "roleCode": "99"
}
```

## 灰度策略配置详细说明
灰度策略配置(`fs-plat-function`)中的关键字段及其要求：

| 字段名称 | 字段apiName | 是否必填 | 备注说明 |
|---------|------------|---------|---------|
| appId | appId | 是 | 固定值: "facishare-system" |
| 功能编码 | functionCode | 是 | 必填且唯一 |
| 操作名称 | functionName | 是 | 便于查看配置，建议必填 |
| 支持的license版本 | supportVersionList | 否 | "*"表示所有license版本都具备 |
| 支持的license模块 | supportModuleList | 否 | "*"表示所有license模块都具备 |
| 是否可编辑 | editable | 否 | 功能权限项是否支持编辑(默认为不可编辑，true表示可编辑) |
| 是否展示 | show | 否 | 功能权限项是否需要展示(默认为展示，false为不展示) |
| 关闭该功能的企业ei | closeEnterpriseIds | 否 | 指定关闭功能的企业ID列表 |
| 支持的企业ea | supportEnterpriseAccounts | 是(与supportEnterpriseIds至少有一个) | 支持灰度企业配置ea，"*"表示全网可用 |
| 支持的企业ei | supportEnterpriseIds | 是(与supportEnterpriseAccounts至少有一个) | 支持灰度企业配置ei，"*"表示全网可用 |

示例JSON结构：
```json
{
  "supportVersionList": [
    "*"
  ],
  "functionCode": "manager_index",
  "functionName": "管理后台首页",
  "supportEnterpriseAccounts": "fktest",
  "supportModuleList": [
    "*"
  ],
  "appId": "facishare-system",
  "isEditable": true,
  "isShow": true
}
```

## 新需求分析：CMS系统菜单接入
根据新的需求文档，我们需要为CMS系统添加管理后台菜单入口，主要内容如下：

1. 功能概述：
   - 增加CMS（Content Management System，内容管理系统）系统
   - 实现外层配置与内层使用的连接
   - 用于管理和发布网页内容的平台

2. 菜单位置：
   - 在"CRM平台管理→界面定制管理"中增加"CMS管理"菜单入口
   
3. 权限控制：
   - 在"角色权限管理→管理功能权限→权限设置→界面定置管理"中，增加"CMS管理"的入口权限
   - 关闭此入口权限时，用户无法对内容进行管理，但仍可在搭建站点时使用这些资源（默认用户有只读权限）
   - 开启此入口权限时，用户可以编辑内容，并在搭建站点时使用这些资源

4. 灰度策略：
   - 仅适用于"无限版"
   - 采用灰度发布策略（而非全网发布）

5. 功能包含：
   - CMS工作区管理（名称、API Name、使用范围等）
   - 工作区中的资源管理（文件夹和图片）
   - 文件操作（上传、移动、编辑、删除）
   - 资源使用（在组件中选择CMS图片资源）

# 高层任务拆分
针对CMS系统的管理后台菜单接入，我们需要完成以下任务：

1. 菜单配置
   - 定义CMS管理菜单的基本信息(menuCode, name, parentCode等)
   - 确定菜单在CRM平台管理→界面定制管理下的具体位置和排序
   - 准备相关多语言支持

2. 权限配置
   - 定义CMS管理的操作权限
   - 配置角色-操作权限关系（系统管理员、CRM管理员等角色）
   - 配置默认权限状态

3. 灰度策略配置
   - 配置仅在"无限版"可见
   - 配置灰度企业可见性
   - 区分不同环境（线上foneshare和专属云）的配置

4. 菜单接入执行计划
   - 定义menuCode: cms_management
   - 定义functionCode: cms_management
   - 定义所需多语言key
   - 准备具体JSON配置

# 项目状态看板
- [x] 对文档结构的整体理解
- [x] 理解菜单数据结构和必填字段
- [x] 理解操作权限数据结构
- [x] 理解角色-操作权限关系配置
- [x] 理解灰度策略配置方式
- [x] 理解多语言支持方式
- [x] 理解菜单下线流程
- [x] 分析新需求的菜单接入要求
- [x] 定义CMS管理菜单配置
- [x] 定义CMS管理操作权限配置
- [x] 定义角色-操作权限关系配置
- [x] 定义灰度策略配置
- [ ] 执行菜单接入操作

# 具体菜单配置方案与实施说明

## 1. 菜单配置

### 菜单数据
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "menuCode": "cms_management",
  "name": "CMS管理",
  "order": 50,
  "parentCode": "ui_customization",
  "route": "crmmanage/=/cms-management"
}
```

### 配置位置与执行命令
1. **配置文件位置**：
   - 通过OSS系统管理界面配置：https://oss.firstshare.cn/paas-console/paas-auth/system
   - 灰度环境(-101)系统库菜单配置

2. **执行步骤**：
   - 登录OSS系统管理界面
   - 导航到"系统库"->"菜单管理"
   - 点击"新增菜单"，填入上述JSON配置
   - 点击保存

3. **或通过API接口配置**：
```bash
curl -X POST "https://oss.firstshare.cn/paas-console/paas-auth/system/menu" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "tenantId": "-101",
    "appId": "facishare-system",
    "menuCode": "cms_management",
    "name": "CMS管理",
    "order": 50,
    "parentCode": "ui_customization",
    "route": "crmmanage/=/cms-management"
  }'
```

## 2. 操作权限配置

### 操作权限数据
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "functionCode": "cms_management",
  "limitScope": false,
  "menuCode": "cms_management",
  "name": "CMS管理",
  "order": 50,
  "parentCode": "00000000000000000000000000000000"
}
```

### 配置位置与执行命令
1. **配置文件位置**：
   - 通过OSS系统管理界面配置：https://oss.firstshare.cn/paas-console/paas-auth/system
   - 灰度环境(-101)系统库功能权限配置

2. **执行步骤**：
   - 登录OSS系统管理界面
   - 导航到"系统库"->"功能权限管理"
   - 点击"新增功能权限"，填入上述JSON配置
   - 点击保存

3. **或通过API接口配置**：
```bash
curl -X POST "https://oss.firstshare.cn/paas-console/paas-auth/system/function" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "tenantId": "-101",
    "appId": "facishare-system",
    "functionCode": "cms_management",
    "limitScope": false,
    "menuCode": "cms_management",
    "name": "CMS管理",
    "order": 50,
    "parentCode": "00000000000000000000000000000000"
  }'
```

## 3. 角色-操作权限关系配置

### 系统管理员权限配置数据
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "functionCodes": [
    "cms_management"
  ],
  "roleCode": "99"
}
```

### CRM管理员权限配置数据
```json
{
  "tenantId": "-101",
  "appId": "facishare-system",
  "functionCodes": [
    "cms_management"
  ],
  "roleCode": "31"
}
```

### 配置位置与执行命令
1. **配置文件位置**：
   - 通过OSS系统管理界面配置：https://oss.firstshare.cn/paas-console/paas-auth/system
   - 灰度环境(-101)系统库角色功能权限关系配置

2. **执行步骤**：
   - 登录OSS系统管理界面
   - 导航到"系统库"->"角色功能权限关系"
   - 点击"新增角色功能权限关系"，分别填入系统管理员和CRM管理员的配置
   - 点击保存

3. **或通过API接口配置**：
```bash
# 系统管理员配置
curl -X POST "https://oss.firstshare.cn/paas-console/paas-auth/system/role-function" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "tenantId": "-101",
    "appId": "facishare-system",
    "functionCodes": [
      "cms_management"
    ],
    "roleCode": "99"
  }'

# CRM管理员配置
curl -X POST "https://oss.firstshare.cn/paas-console/paas-auth/system/role-function" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "tenantId": "-101",
    "appId": "facishare-system",
    "functionCodes": [
      "cms_management"
    ],
    "roleCode": "31"
  }'
```

## 4. 灰度策略配置

### 灰度策略数据
```json
{
  "supportVersionList": [
    "无限版"
  ],
  "functionCode": "cms_management",
  "functionName": "CMS管理",
  "supportEnterpriseAccounts": "fktest",
  "supportModuleList": [
    "*"
  ],
  "appId": "facishare-system",
  "isEditable": true,
  "isShow": true
}
```

### 配置位置与执行命令
1. **配置文件位置**：
   - 配置文件：fs-plat-function
   - 不同环境的配置地址：
     - 112环境: http://oss.firstshare.cn/cms/edit/config/15833
     - 线上foneshare环境: http://oss.foneshare.cn/cms/edit/config/15855
     - 线上专属云环境: http://oss.foneshare.cn/cms/edit/config/15858

2. **执行步骤**：
   - 访问对应环境的配置地址
   - 编辑fs-plat-function配置文件
   - 添加上述JSON配置
   - 保存配置

## 5. 多语言配置

### 多语言key
1. 菜单名称多语言key: 
   - management.menu.facishare-system.cms_management

2. 操作权限多语言key:
   - management.function.facishare-system.cms_management

### 配置位置与执行命令
1. **配置位置**：
   - 多语言平台：http://oss.firstshare.cn/i18n-console/code/add/

2. **执行步骤**：
   - 访问多语言平台
   - 添加菜单名称多语言key
   - 添加操作权限多语言key
   - 分别填入中文、英文等语言的翻译
   - 保存配置

## 6. 缓存管理

### 配置位置与执行命令
1. **配置文件位置**：
   - fs-paas-auth配置文件

2. **执行步骤**：
   - 登录对应环境的CMS系统
   - 编辑fs-paas-auth配置文件
   - 设置refresh-system-cache=true
   - 等待30秒
   - 设置refresh-system-cache=false
   - 等待30秒
   - 设置refresh-system-cache=true

## 7. 各环境配置顺序

1. **测试环境配置**：
   - 先在测试环境完成菜单、权限、角色关系、灰度策略配置
   - 验证配置正确性及功能可用性

2. **灰度环境配置**：
   - 在灰度环境(-101)重复上述配置步骤
   - 验证灰度企业可正常使用功能

3. **全网环境配置**：
   - 功能稳定后，根据需要在全网系统库(-100)配置

4. **专属云环境配置**：
   - 在专属云环境配置，注意全部用【-100】系统库

# 执行者反馈或请求帮助
规划者已经完成了对CMS系统菜单接入的详细规划，包括具体的配置方式和执行命令。请用户确认以上方案是否合适。确认后，执行者将开始按照规划执行菜单接入操作。

# 经验教训
- 890版本后，权限和菜单的code需要去掉斜杠"/"、"="、"-"，否则无法录入多语词条
- 要特别注意【-100】和【-101】的区别：【-100】是全网企业都使用的，【-101】是大版本灰度企业使用的
- 专属云全部都是用【-100】
- 不要忘记预置线上专属云的菜单，尤其是新增云环境的情况
- 多语言key的命名必须符合规范，否则国际化将无法正常工作
- 菜单顺序调整或下线后，必须清除缓存才能生效
- 菜单顺序调整需要提交审批流程，执行SQL语句操作
- 新菜单接入时如果涉及到图标资源，需要严格遵守图标规格要求
- 灰度策略配置需要考虑license版本和企业账号/ID，确保只有目标用户可见
- 配置完成后一定要测试验证菜单是否正常显示及功能是否可用 